import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { radius, spacing, typography, useTheme } from '../theme';

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <View style={styles.systemMessageContainer}>
        <View style={[
          styles.systemMessageContent,
          message.status === 'sending' && styles.sendingGradient
        ]}>
          <Text style={styles.systemMessageText}>
            Chat cleared by {message.is_mine ? 'you' : message.sender_username}
          </Text>
          <Text style={styles.messageTime}>{formatTime(message.timestamp)}</Text>
        </View>
      </View>
    );
  }

  if (message.type === 'typing') return null;

  const renderStatusIcon = () => {
    if (!message.isMine && !message.is_mine) return null;

    switch (message.status) {
      case 'sending':
        return (
          <View style={styles.statusContainer}>
            <ActivityIndicator size="small" color={colors.myMessageTick} />
          </View>
        );
      case 'sent':
        return (
          <View style={styles.statusContainer}>
            <Text style={[styles.singleTick, { color: colors.myMessageTick }]}>✓</Text>
          </View>
        );
      case 'delivered':
      case 'read':
        return (
          <View style={styles.statusContainer}>
            <Text style={[styles.doubleTick, { color: colors.success }]}>✓✓</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const isMine = message.isMine || message.is_mine;

  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient
      ]}>
        <Text style={[
          styles.messageText,
          isMine ? styles.sentText : styles.receivedText
        ]}>
          {message.message}
        </Text>
        <View style={styles.messageInfo}>
          <Text style={[
            styles.messageTime,
            { color: isMine ? colors.myMessageTime : colors.peerMessageTime }
          ]}>
            {formatTime(message.timestamp)}
          </Text>
          {renderStatusIcon()}
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 2,
    marginHorizontal: spacing.md,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: spacing.sm,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: radius.md, // 12px to match frontend
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  sentContent: {
    backgroundColor: colors.myMessageBg, // Use new message-specific colors
  },
  receivedContent: {
    backgroundColor: colors.peerMessageBg, // Use new message-specific colors
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  sentText: {
    color: colors.myMessageText, // Use new message-specific colors
  },
  receivedText: {
    color: colors.peerMessageText, // Use new message-specific colors
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  messageTime: {
    ...typography.caption,
    marginRight: spacing.xs,
  },
  statusContainer: {
    marginLeft: spacing.xs,
  },
  singleTick: {
    fontSize: 12,
  },
  doubleTick: {
    fontSize: 12,
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  systemMessageContent: {
    backgroundColor: colors.gray100,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default MessageItem;
