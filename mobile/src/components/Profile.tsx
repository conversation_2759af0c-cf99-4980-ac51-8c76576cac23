import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout, selectAuthUser } from '../redux/slices/authSlice';
import { ProfileNavigationProp, ProfileRouteProp } from '../navigation/types';
import { colors, shadows, radius, spacing, typography } from '../theme';
import LogoutModal from './LogoutModal';
import ClearChatModal from './ClearChatModal';

interface ProfileProps {
  navigation: ProfileNavigationProp;
  route: ProfileRouteProp;
}

const Profile: React.FC<ProfileProps> = ({ navigation, route }) => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectAuthUser);
  const { username } = route.params;
  
  const [showLogoutModal, setShowLogoutModal] = useState<boolean>(false);
  const [showClearDataModal, setShowClearDataModal] = useState<boolean>(false);
  const [clearingData, setClearingData] = useState<boolean>(false);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  const confirmLogout = () => {
    dispatch(logout());
    setShowLogoutModal(false);
  };

  const handleClearData = () => {
    setShowClearDataModal(true);
  };

  const confirmClearData = async () => {
    try {
      setClearingData(true);
      // TODO: Implement clear data functionality
      Alert.alert('Success', 'All data has been cleared');
      setShowClearDataModal(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to clear data');
    } finally {
      setClearingData(false);
    }
  };

  const getAvatarText = () => {
    return username?.charAt(0).toUpperCase() || '?';
  };

  const isOwnProfile = currentUser === username;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Info */}
        <View style={styles.userSection}>
          <View style={styles.userAvatar}>
            <Text style={styles.avatarText}>{getAvatarText()}</Text>
          </View>
          <Text style={styles.username}>{username}</Text>
        </View>

        {/* Settings Sections - Only show for own profile */}
        {isOwnProfile && (
          <>
            {/* Privacy Section */}
            <View style={styles.settingGroup}>
              <Text style={styles.groupTitle}>Privacy</Text>
              
              <TouchableOpacity style={styles.settingItem}>
                <Text style={styles.settingLabel}>Recall Limit</Text>
                <Text style={styles.settingValue}>Set Limit</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.settingItem} onPress={handleClearData}>
                <Text style={styles.settingLabel}>Reset Messages</Text>
                <Text style={styles.settingValue}>🔄</Text>
              </TouchableOpacity>
            </View>

            {/* Notifications Section */}
            <View style={styles.settingGroup}>
              <Text style={styles.groupTitle}>Notifications</Text>
              
              <TouchableOpacity style={styles.settingItem}>
                <Text style={styles.settingLabel}>Push Notifications</Text>
                <Text style={styles.settingValue}>On</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.settingItem}>
                <Text style={styles.settingLabel}>Emoji Reactions</Text>
                <Text style={styles.settingValue}>On</Text>
              </TouchableOpacity>
            </View>

            {/* Safety & Support Section */}
            <View style={styles.settingGroup}>
              <Text style={styles.groupTitle}>Safety & Support</Text>
              
              <TouchableOpacity style={styles.settingItem}>
                <Text style={styles.settingLabel}>Block</Text>
                <Text style={styles.settingValue}></Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.settingItem}>
                <Text style={styles.settingLabel}>Report</Text>
                <Text style={styles.settingValue}></Text>
              </TouchableOpacity>

              <TouchableOpacity style={[styles.settingItem, styles.dangerItem]}>
                <Text style={[styles.settingLabel, styles.dangerText]}>Delete User</Text>
                <Text style={styles.settingValue}></Text>
              </TouchableOpacity>
            </View>

            {/* Logout Section */}
            <View style={styles.logoutSection}>
              <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                <Text style={styles.logoutButtonText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {/* For other users' profiles */}
        {!isOwnProfile && (
          <View style={styles.settingGroup}>
            <Text style={styles.groupTitle}>Actions</Text>
            
            <TouchableOpacity style={styles.settingItem}>
              <Text style={styles.settingLabel}>Block User</Text>
              <Text style={styles.settingValue}></Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.settingItem}>
              <Text style={styles.settingLabel}>Report User</Text>
              <Text style={styles.settingValue}></Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Modals */}
      <LogoutModal
        isVisible={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        onConfirm={confirmLogout}
      />

      <ClearChatModal
        isVisible={showClearDataModal}
        onClose={() => setShowClearDataModal(false)}
        onConfirm={confirmClearData}
        loading={clearingData}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.sm,
  },
  backButton: {
    padding: spacing.xs,
  },
  backButtonText: {
    fontSize: 24,
    color: colors.primary,
  },
  headerTitle: {
    ...typography.h2,
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40, // Same width as back button for centering
  },
  content: {
    flex: 1,
  },
  userSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    backgroundColor: colors.cardBackground,
    marginBottom: spacing.md,
  },
  userAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    ...shadows.md,
  },
  avatarText: {
    color: colors.white,
    fontSize: 32,
    fontWeight: 'bold',
  },
  username: {
    ...typography.h1,
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  settingGroup: {
    backgroundColor: colors.cardBackground,
    marginBottom: spacing.md,
    paddingVertical: spacing.sm,
  },
  groupTitle: {
    ...typography.caption,
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    letterSpacing: 0.5,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    textTransform: 'uppercase',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingLabel: {
    ...typography.body,
    color: colors.text,
    flex: 1,
  },
  settingValue: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '500',
  },
  dangerItem: {
    borderBottomWidth: 0,
  },
  dangerText: {
    color: colors.danger,
  },
  logoutSection: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  logoutButton: {
    backgroundColor: colors.danger,
    paddingVertical: spacing.md,
    borderRadius: radius.sm,
    alignItems: 'center',
    ...shadows.sm,
  },
  logoutButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Profile;
