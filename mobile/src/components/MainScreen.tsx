import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout, selectAuthUser, selectAuthToken } from '../redux/slices/authSlice';
import {
  setInitialized,
  selectDBInitialized,
  initializeDatabase
} from '../redux/slices/chatDBSlice';
import { connectRequest, disconnectRequest, selectConnected } from '../redux/slices/socketSlice';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import { chatDBService } from '../database/service';
import RoomsList from './RoomsList';
import NewChatModal from './NewChatModal';
import { MainScreenNavigationProp } from '../navigation/types';
import { shadows, radius, spacing, typography, useTheme } from '../theme';

interface MainScreenProps {
  navigation: MainScreenNavigationProp;
}

const MainScreen: React.FC<MainScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectAuthUser);
  const authToken = useAppSelector(selectAuthToken);
  const dbInitialized = useAppSelector(selectDBInitialized);
  const connected = useAppSelector(selectConnected);
  const { colors } = useTheme();
  const [selectedUsername, setSelectedUsername] = useState<string | null>(null);
  const [showNewChatModal, setShowNewChatModal] = useState<boolean>(false);

  // Use WatermelonDB observables to get rooms
  const rooms = useWatermelonObservable(
    user && dbInitialized ? chatDBService.observeRooms(user) : null,
    []
  );

  // Initialize database on component mount
  useEffect(() => {
    const initDB = async () => {
      try {
        const success = await initializeDatabase();
        dispatch(setInitialized(success));
        if (success) {
          console.log('Database initialized successfully');
        } else {
          console.error('Failed to initialize database');
        }
      } catch (error) {
        console.error('Database initialization error:', error);
        dispatch(setInitialized(false));
      }
    };

    if (!dbInitialized) {
      initDB();
    }
  }, [dispatch, dbInitialized]);

  // Manage socket connection at the app level
  useEffect(() => {
    // Connect to socket when authenticated and database is initialized
    if (user && authToken && dbInitialized) {
      console.log('Connecting to socket from MainScreen component');
      dispatch(connectRequest({ authToken }));
    }

    // Disconnect when the component unmounts or user logs out
    return () => {
      if (connected) {
        console.log('Disconnecting socket from MainScreen component');
        dispatch(disconnectRequest());
      }
    };
  }, [user, authToken, dbInitialized, dispatch]);

  // Auto-reconnect mechanism
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout | null = null;

    // If authenticated but not connected, try to reconnect
    if (user && authToken && dbInitialized && !connected) {
      console.log('Socket disconnected, attempting to reconnect...');
      reconnectTimer = setTimeout(() => {
        console.log('Reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 3000); // Try to reconnect after 3 seconds
    }

    // Clean up timer on unmount
    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
    };
  }, [user, authToken, dbInitialized, connected, dispatch]);

  const handleProfilePress = () => {
    // Navigate to profile screen with current user
    if (user) {
      navigation.navigate('Profile', { username: user });
    }
  };

  const handleRoomSelect = (username: string) => {
    setSelectedUsername(username);
    // Navigate to chat screen with the selected user
    navigation.navigate('ChatRoom', { username });
  };

  const handleNewChat = () => {
    setShowNewChatModal(true);
  };

  const handleCloseModal = () => {
    setShowNewChatModal(false);
  };

  const handleStartChat = (username: string) => {
    // Use the existing room select logic to start a new chat
    handleRoomSelect(username);
    setShowNewChatModal(false);
  };

  if (!dbInitialized) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Initializing database...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const getAvatarText = () => {
    return user?.charAt(0).toUpperCase() || '?';
  };

  const styles = createStyles(colors);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity style={styles.profileSection} onPress={handleProfilePress}>
            <View style={styles.userAvatar}>
              <Text style={styles.avatarText}>{getAvatarText()}</Text>
            </View>
            <Text style={styles.username}>{user}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.newChatButton} onPress={handleNewChat}>
            <Text style={styles.newChatButtonText}>+</Text>
          </TouchableOpacity>
        </View>

      </View>

      <View style={styles.roomsContainer}>
        <View style={styles.chatsHeader}>
          <Text style={styles.chatsHeaderText}>CHATS</Text>
        </View>
        <RoomsList
          rooms={rooms}
          onRoomSelect={handleRoomSelect}
          selectedUsername={selectedUsername}
          onNewChat={handleNewChat}
        />
      </View>

      <NewChatModal
        isVisible={showNewChatModal}
        onClose={handleCloseModal}
        onStartChat={handleStartChat}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  loadingText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  header: {
    backgroundColor: colors.cardBackground,
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: radius.round,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  avatarText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  username: {
    ...typography.h2,
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  newChatButton: {
    width: 40,
    height: 40,
    borderRadius: radius.round,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.sm,
  },
  newChatButtonText: {
    color: colors.white,
    fontSize: 24,
    fontWeight: 'bold',
  },
  roomsContainer: {
    flex: 1,
  },
  chatsHeader: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  chatsHeaderText: {
    ...typography.caption,
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    letterSpacing: 0.5,
  },
});

export default MainScreen;
