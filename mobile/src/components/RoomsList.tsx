import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import RoomItem from './RoomItem';
import { useTheme } from '../theme';

interface Room {
  id: string;
  username: string;
  last_msg: string;
  updated: number;
  unread_count?: number;
}

interface RoomsListProps {
  rooms: Record<string, any>[];
  onRoomSelect: (username: string) => void;
  selectedUsername?: string | null;
  onNewChat?: () => void;
}

const RoomsList: React.FC<RoomsListProps> = ({
  rooms,
  onRoomSelect,
  selectedUsername,
  onNewChat,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderRoom = ({ item }: { item: Record<string, any> }) => (
    <RoomItem
      room={item}
      onPress={onRoomSelect}
      isSelected={item.username === selectedUsername}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📱</Text>
      <Text style={styles.emptyTitle}>No conversations yet</Text>
      <Text style={styles.emptySubtitle}>Start chatting with someone!</Text>
      {onNewChat && (
        <TouchableOpacity style={styles.startChatButton} onPress={onNewChat}>
          <Text style={styles.startChatButtonText}>Start New Chat</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={rooms}
        renderItem={renderRoom}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={rooms.length === 0 ? styles.emptyListContainer : undefined}
      />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  emptyListContainer: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  emptyIcon: {
    fontSize: 36,
    marginBottom: 10,
    color: colors.primary,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.textSecondary,
    marginBottom: 3,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  startChatButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  startChatButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default RoomsList;
