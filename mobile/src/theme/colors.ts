/**
 * Theme colors with support for light and dark modes
 * These colors match the CSS variables defined in chatspot-frontend/src/index.css
 */

// Light theme colors
export const lightColors = {
  // Primary colors
  primary: '#F0503E',
  primaryDark: '#b53729',
  primaryTint: '#fceded',
  primaryLight: '#f8cac8',
  primaryTone: '#928585',

  // Tint colors
  tint: '#928585',
  tintLight: '#f1f0f0',

  // Shade colors
  shade: '#272222',
  shadeLight: '#d7d4d4',
  shadeTwo: '#494040',

  // Secondary colors
  secondary: '#238b97',
  secondaryLight: '#d3f7fe',

  // Status colors
  danger: '#d32f2f',
  dangerHover: '#b71c1c',
  success: '#4CAF50',
  warning: '#FF5722',

  // Background colors
  background: '#f0f0f0',
  cardBackground: '#ffffff',

  // Text colors
  text: '#333',
  textSecondary: '#757575',
  textSubtle: '#666666',

  // Border colors
  border: '#e0e0e0',
  inputBorder: '#D0D0D0',

  // Button colors
  buttonSecondaryBg: '#f5f5f5',
  buttonSecondaryText: '#333',
  buttonSecondaryBorder: '#d0d0d0',
  buttonSecondaryHoverBg: '#e8e8e8',

  // Message colors (matching frontend)
  myMessageBg: 'rgba(255, 69, 0, 0.2)', // matches .my-message background
  myMessageText: '#F0503E', // matches .my-message color
  myMessageTime: '#b53729', // darker red for better visibility
  myMessageTick: '#b53729', // darker red for better visibility
  peerMessageBg: '#e9e9e9', // matches .peer-message background
  peerMessageText: '#333', // matches .peer-message color
  peerMessageTime: '#757575', // gray for peer message time

  // Image styling colors
  imageBg: 'rgba(255, 255, 255, 0.8)', // matches .image-div background

  // Connection status
  connected: '#4CAF50',
  disconnected: '#FF5722',

  // Common grays
  gray100: '#f5f5f5',
  gray200: '#e0e0e0',
  gray300: '#d0d0d0',
  gray400: '#999999',
  gray500: '#666666',
  gray600: '#333333',

  // White and black
  white: '#ffffff',
  black: '#000000',

  // Transparent variants
  transparent: 'transparent',
  overlay: 'rgba(0, 0, 0, 0.5)',
} as const;

// Dark theme colors
export const darkColors = {
  // Primary colors
  primary: '#F0503E',
  primaryDark: '#b53729',
  primaryTint: '#2a1a1a',
  primaryLight: '#3a2020',
  primaryTone: '#928585',

  // Tint colors
  tint: '#928585',
  tintLight: '#2a2a2a',

  // Shade colors
  shade: '#e0e0e0',
  shadeLight: '#404040',
  shadeTwo: '#b0b0b0',

  // Secondary colors
  secondary: '#238b97',
  secondaryLight: '#1a2a2c',

  // Status colors
  danger: '#f44336',
  dangerHover: '#d32f2f',
  success: '#4CAF50',
  warning: '#FF5722',

  // Background colors
  background: '#121212',
  cardBackground: '#1e1e1e',

  // Text colors
  text: '#ffffff',
  textSecondary: '#b0b0b0',
  textSubtle: '#888888',

  // Border colors
  border: '#333333',
  inputBorder: '#404040',

  // Button colors
  buttonSecondaryBg: '#2a2a2a',
  buttonSecondaryText: '#ffffff',
  buttonSecondaryBorder: '#404040',
  buttonSecondaryHoverBg: '#333333',

  // Message colors for dark theme
  myMessageBg: 'rgba(240, 80, 62, 0.3)',
  myMessageText: '#F0503E',
  myMessageTime: '#ff8a80', // lighter red for better visibility in dark
  myMessageTick: '#ff8a80', // lighter red for better visibility in dark
  peerMessageBg: '#2a2a2a',
  peerMessageText: '#ffffff',
  peerMessageTime: '#b0b0b0', // light gray for peer message time

  // Image styling colors
  imageBg: 'rgba(255, 255, 255, 0.1)',

  // Connection status
  connected: '#4CAF50',
  disconnected: '#FF5722',

  // Common grays
  gray100: '#2a2a2a',
  gray200: '#333333',
  gray300: '#404040',
  gray400: '#666666',
  gray500: '#888888',
  gray600: '#b0b0b0',

  // White and black
  white: '#ffffff',
  black: '#000000',

  // Transparent variants
  transparent: 'transparent',
  overlay: 'rgba(0, 0, 0, 0.7)',
} as const;

// Default to light colors for backward compatibility
export const colors = lightColors;

// Shadow definitions (matching frontend CSS variables)
export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.1,
    shadowRadius: 15,
    elevation: 8,
  },
} as const;

// Border radius values (matching frontend CSS variables)
export const radius = {
  sm: 4,
  md: 8,
  lg: 16,
  xl: 24,
  round: 50, // for circular elements
} as const;

// Spacing values (matching frontend CSS variables)
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
} as const;

export type ColorKey = keyof typeof colors;
export type ShadowKey = keyof typeof shadows;
export type RadiusKey = keyof typeof radius;
export type SpacingKey = keyof typeof spacing;
