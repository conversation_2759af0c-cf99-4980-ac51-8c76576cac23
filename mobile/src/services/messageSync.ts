import { messageAP<PERSON> } from './api';
import { chatDBService } from '../database/service';
import { debugLog } from '../utils/env';

export interface MessageSyncData {
  id: string;
  sender_username: string;
  receiver_username: string;
  message: string;
  type: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';
  timestamp: string;
  status: 'pending' | 'delivered';
  delivered_at: string | null;
}

export const messageSyncService = {
  /**
   * Sync all messages from the server to WatermelonDB
   * This replaces the socket-based offline message sync
   */
  syncAllMessages: async (currentUsername: string): Promise<void> => {
    try {
      debugLog('Starting message sync for user:', currentUsername);

      // Fetch all messages from the server
      const serverMessages: MessageSyncData[] = await messageAPI.getAllMessages();
      debugLog(`Fetched ${serverMessages.length} messages from server`);

      if (serverMessages.length === 0) {
        debugLog('No messages to sync');
        return;
      }

      // Process each message
      for (const serverMessage of serverMessages) {
        await messageSyncService.syncSingleMessage(serverMessage, currentUsername);
      }

      debugLog('Message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync messages:', error);
      throw error;
    }
  },

  /**
   * Sync a single message to WatermelonDB
   * Handles both new messages and status updates
   */
  syncSingleMessage: async (serverMessage: MessageSyncData, currentUsername: string): Promise<void> => {
    try {
      const {
        id: serverId,
        sender_username,
        receiver_username,
        message,
        type,
        timestamp,
        status,
      } = serverMessage;

      // Convert timestamp to number
      const timestampNum = new Date(timestamp).getTime();

      // Determine if this is the current user's message
      const isMine = sender_username === currentUsername;

      // Check if message already exists in local database
      const existingMessages = await chatDBService.getMessages(sender_username, receiver_username);
      const existingMessage = existingMessages.find(msg =>
        msg.sender_username === sender_username &&
        msg.receiver_username === receiver_username &&
        msg.message === message &&
        Math.abs(msg.timestamp - timestampNum) < 1000 // Allow 1 second difference
      );

      if (existingMessage) {
        // Message exists, check if we need to update status
        if (existingMessage.status !== status) {
          debugLog(`Updating message status from ${existingMessage.status} to ${status}`);
          await chatDBService.updateMessageStatus(existingMessage.id, status);
        }
      } else {
        // New message, create it
        debugLog(`Creating new message: ${message.substring(0, 50)}...`);
        await chatDBService.saveMessage(
          sender_username,
          receiver_username,
          message,
          isMine,
          type,
          currentUsername // selectedUser parameter
        );
      }
    } catch (error) {
      console.error('Failed to sync single message:', error);
      // Don't throw here to allow other messages to continue syncing
    }
  },

  /**
   * Sync messages for a specific conversation
   */
  syncConversationMessages: async (currentUsername: string, otherUsername: string): Promise<void> => {
    try {
      debugLog(`Syncing conversation messages between ${currentUsername} and ${otherUsername}`);

      // Fetch conversation messages from the server
      const serverMessages: MessageSyncData[] = await messageAPI.getConversationMessages(otherUsername);
      debugLog(`Fetched ${serverMessages.length} conversation messages from server`);

      if (serverMessages.length === 0) {
        debugLog('No conversation messages to sync');
        return;
      }

      // Process each message
      for (const serverMessage of serverMessages) {
        await messageSyncService.syncSingleMessage(serverMessage, currentUsername);
      }

      debugLog('Conversation message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync conversation messages:', error);
      throw error;
    }
  },

  /**
   * Sync only pending messages (for quick updates)
   */
  syncPendingMessages: async (currentUsername: string): Promise<void> => {
    try {
      debugLog('Syncing pending messages for user:', currentUsername);

      // Fetch pending messages from the server
      const pendingMessages: MessageSyncData[] = await messageAPI.getPendingMessages();
      debugLog(`Fetched ${pendingMessages.length} pending messages from server`);

      if (pendingMessages.length === 0) {
        debugLog('No pending messages to sync');
        return;
      }

      // Process each pending message
      for (const pendingMessage of pendingMessages) {
        await messageSyncService.syncSingleMessage(pendingMessage, currentUsername);
      }

      debugLog('Pending message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync pending messages:', error);
      throw error;
    }
  },

  /**
   * Sync delivered messages to get delivery confirmations for messages sent while offline
   * This will trigger delivery confirmations and clean up the backend
   */
  syncDeliveredMessages: async (currentUsername: string): Promise<void> => {
    try {
      debugLog('Syncing delivered messages for user:', currentUsername);

      // Fetch delivered messages from the server
      // This will automatically send delivery confirmations and trigger cleanup
      const deliveredMessages: MessageSyncData[] = await messageAPI.getDeliveredMessages();
      debugLog(`Fetched ${deliveredMessages.length} delivered messages from server`);

      if (deliveredMessages.length === 0) {
        debugLog('No delivered messages to sync');
        return;
      }

      debugLog(`Found ${deliveredMessages.length} messages that were delivered while offline`);
      debugLog('Delivery confirmations will be sent via socket events');

      // Note: The actual delivery confirmations are sent via socket events from the backend
      // when we call the /api/messages/delivered endpoint. The frontend will receive
      // 'message_delivered' events and update the local database accordingly.

      debugLog('Delivered message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync delivered messages:', error);
      throw error;
    }
  }
};
