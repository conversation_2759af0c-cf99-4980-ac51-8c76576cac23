import axios, { AxiosInstance, AxiosError } from 'axios';
import { getApiUrl, debugLog } from '../utils/env';
import { authStorage } from '../utils/storage';

// Get API URL from environment utility
const API_URL = getApiUrl();

// Create axios instance with default config
const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor to include auth token in requests
api.interceptors.request.use(
  async (config) => {
    const token = await authStorage.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    // Handle specific error cases
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      debugLog('Response error:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      debugLog('Request error:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      debugLog('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

interface AuthResponse {
  access_token: string;
  [key: string]: any;
}

// Authentication API services
export const authService = {
  // Login with username and password
  login: async (username: string, password: string): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/login', { username, password });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Login failed';
    }
  },

  // Register with username and password
  register: async (username: string, password: string): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/register', { username, password });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Registration failed';
    }
  }
};

// Message API endpoints
export const messageAPI = {
  // Get all messages for the authenticated user
  getAllMessages: async () => {
    const response = await api.get('/api/messages');
    return response.data;
  },

  // Get messages for a specific conversation
  getConversationMessages: async (otherUsername: string) => {
    const response = await api.get(`/api/messages/conversation/${otherUsername}`);
    return response.data;
  },

  // Get pending messages for the authenticated user
  getPendingMessages: async () => {
    const response = await api.get('/api/messages/pending');
    return response.data;
  },

  // Get delivered messages for the authenticated user
  getDeliveredMessages: async () => {
    const response = await api.get('/api/messages/delivered');
    return response.data;
  },

  // Mark messages as delivered
  markAsDelivered: async (messageIds: string[]) => {
    const response = await api.post('/api/messages/delivered', { messageIds });
    return response.data;
  },

  // Mark messages as read
  markAsRead: async (messageIds: string[]) => {
    const response = await api.post('/api/messages/read', { messageIds });
    return response.data;
  },

  // Delete messages
  deleteMessages: async (messageIds: string[]) => {
    const response = await api.delete('/api/messages', { data: { messageIds } });
    return response.data;
  }
};

// Export the API instance for other services
export default api;
