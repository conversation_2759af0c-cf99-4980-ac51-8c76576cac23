/**
 * ChatSpot Mobile App
 * Main application component with Redux and authentication
 */

import React, { useEffect, useState } from 'react';
import { StatusBar, SafeAreaView } from 'react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

import store from './src/redux/store';
import { useAppDispatch, useAppSelector } from './src/hooks/redux';
import {
  initializeAuth,
  selectIsAuthenticated
} from './src/redux/slices/authSlice';
import { authStorage } from './src/utils/storage';
import { ThemeProvider, useTheme } from './src/theme';

import Login from './src/components/Login';
import Register from './src/components/Register';
import MainScreen from './src/components/MainScreen';
import ChatRoom from './src/components/ChatRoom';
import Profile from './src/components/Profile';
import LoadingScreen from './src/components/LoadingScreen';
import { RootStackParamList } from './src/navigation/types';

const Stack = createStackNavigator<RootStackParamList>();

// Main App Component (inside Redux Provider)
const AppContent: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [currentScreen, setCurrentScreen] = useState<'login' | 'register'>('login');
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const { isDark, colors } = useTheme();

  // Initialize auth state from storage on app start
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const token = await authStorage.getToken();
        const username = await authStorage.getUsername();

        dispatch(initializeAuth({ token, username }));
      } catch (error) {
        console.error('Error initializing app:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, [dispatch]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  const navigateToRegister = () => setCurrentScreen('register');
  const navigateToLogin = () => setCurrentScreen('login');

  return (
    <NavigationContainer>
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
        <StatusBar
          barStyle={isDark ? "light-content" : "dark-content"}
          backgroundColor={colors.background}
        />
        {isAuthenticated ? (
          <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name="MainScreen" component={MainScreen} />
            <Stack.Screen name="ChatRoom" component={ChatRoom} />
            <Stack.Screen name="Profile" component={Profile} />
          </Stack.Navigator>
        ) : currentScreen === 'login' ? (
          <Login navigation={{ navigate: navigateToRegister }} />
        ) : (
          <Register navigation={{ navigate: navigateToLogin }} />
        )}
      </SafeAreaView>
    </NavigationContainer>
  );
};

// Root App Component with Providers
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
};

export default App;
